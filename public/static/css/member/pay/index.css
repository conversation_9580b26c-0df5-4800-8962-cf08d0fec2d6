/* 自助付款页面样式 - 与原版本保持一致 */

/* 基础样式重置 */
body,dd,div,dl,dt,footer,h1,h2,h3,h4,h5,header,iframe,img,label,li,ol,p,section,span,time,ul {
  word-wrap: break-word;
  word-break: break-all;
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none;
  outline: none;
  font-style: normal;
  font-weight: 400;
}

body,html {
  height: 100%;
}

body {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  font-size: 0.6rem;
  line-height: 1.2;
  overflow-x: hidden;
  font-family: Helvetica Neue,Helvetica,Arial,sans-serif;
  background-color: #f7f7f7;
}

:active,:focus {
  outline: none;
}

li,ol,ul {
  list-style: none;
}

a,a:hover,a:link,a:visited {
  text-decoration: none;
  color: #fff;
}

a img,img {
  border: none;
}

/* Vue应用容器 - 全屏显示 */
#app {
  min-height: 100vh;
  width: 100%;
  background-color: #f7f7f7;
}

/* 隐藏未编译的Vue模板 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 100vh;
  background: #f7f7f7;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #fe9b20;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  font-size: 0.8rem;
}

/* 原版本的loading样式 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  z-index: 9999;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* 页面容器 - 全屏宽度 */
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f7f7;
  position: relative;
}

/* 店铺信息 - 与原版本保持一致 */
.shop-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0.5rem;
  height: 2rem;
  background: #fff;
}

.shop-logo {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAMAAAC5zwKfAAABOFBMVEUAAAC43f243P2+4f/e//q43f233f243f253v/G4//83rq33P244/////+33P3/4r243Py43fy43Py43f3/4r643f253f243v243f233f+53v+63/+/5v//68z/4r7/4r394Lu33v/83rnz1rL/4b3/4bz73bn/4rz/4r3/477/5r//5r3/5sS33Pz+myD/4bzy1bJ/qdr/////nqHy8vLylZj/7914oM58pdbymZzy8O6UVjP22bV6otHy6uDy3sXyyLjyqqr/+/v/q67/pajy49P/v8HyurT5/P7/3+D/1db/69P/ycv/oqX3ojjm8/7N5/283vzB2fX/8/T/6erXzt//tLfI4vji7PTfz97tv8f3u8DLyr3MybvwrLD2ozn3ojf2+v7V5/b/8fH/yMn/uLvkzbyZAAAAL3RSTlMA3+QdBvDSk00J/tUSAfnv7ODFu7Gdko9xbGhSRT8UCE/SbydH4sW7sZ1oPxQfHo0V0bEAAAOJSURBVFjDrZjnVtswFIDlkMTZe+8Eym6FA3SQNGSyCyktlFVW1/u/QX1iS5Ytx5bH90u6ufpOZN1jyQIm1CpFPu3nErFYgvOn+WKlBuwTDOT9kMKfDwTt2KLVrA/OwZetRi3qvCUOGsKVvFbmWvBAUzwF5pkHOMgEF2DSLWQgM5kFc1/ZBy3gK5stBg8twhsuTiQFLZOKGDy+MLRBeO6DXPRAW3gW5/w/5LNu1P2PkTC0TTiis74p6IAUvdY8dARP1TN0SFmzID6nQp96YTLQMRnV+wW6QEDxBTlI8DTdYuTlERJwyvuxAElet5iZQpICLkGPKr5lAUjiQcVYgu4IYUne3zi3hJy0F1ahW0JYnQmz7gmzs5rxuSf0BVFROxcqxZ13U5gXhdR5aMrue6VOUgDUoJbHF1bf9AlqqYEK1OezCLT+WwUU3RUWta/+y187M/5Mdmcc7cjcSP3JWO7/lfs/tFtBWtX/diLnH+1qhLsyp3L/VO4ffFcJ0ppF/iqnX6HxZ1rhOQpM5MBPzTJzqgmj7HM0/lorvEGBMxS5Jw0cSOhNeHwgJ1+MsVCJKDn0pBMgRk1Y5Ez5P1iIuFZmoTPpGCm8xKMn+IkpQuqpXuHQPSlMUBNGK4jWFAmVdceVQE86AThiwoh/ePQVLZzg0DWOPSiLQpTNCc68wJljWniBQ2McO1DKhijs95gPGNYYUdi8u0IeFN0VFkHFXWEF1Ghht4PyOl3GGPGCBX43hX5yk7pDif0RShz12WLPeJMit9FblDgYosThgC2G37IB1Ub/W8o7bLePpbzjdvuQJfagbPTqo8jtXbfbH7RFhqNOZzRsiwz6ZrFnZRfIUoeltg2ow1KUcyakjnOgpBH29j/J7Ou1esjU62mEJXwkVgtDe2jwnl4rhIQhqUUfiUFBJewJ23vy2G29ltCT/6AgtSCioPNZIaaFBEEcvS/OTbTotQQh1JPypBb1WUEWtzBj2xBBg1LUBBnnwgz18ehASH88grJTYRlo4J0JeZ0rAifClFfvEsO+MBzRv2axK/QszLsImgm/fDRDI/Qszr+qsiNUrqpoIsvWhcsRYIA3Z1WYM7uLbcR7bTMUXbwBTHm7yi5cfQdYWHrDJkw2ACP1lrFS0rXqgJ36elIwJLmOdKxEN9bi82zxtY0osEG9kWvStmauUQf22Vxq5VaayXgoFE82V3KtpU1gzH/BYtJVdPvPqQAAAABJRU5ErkJggg==");
  background-size: 2rem 2rem;
  width: 2rem;
  height: 2rem;
}

.shop-name {
  font-size: 0.85rem;
  line-height: 2rem;
  margin-left: 0.5rem;
  color: #333;
}

/* 主容器 */
.container {
  background: #fff;
  padding: 1rem;
}

/* 金额输入区域 - 与原版本保持一致 */
.amount-section {
  background: #fff;
}

.amount-box {
  background: #fff;
  padding: 0;
}

.input-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.label {
  font-size: 0.8rem;
  color: #666;
}

.amount-display {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  position: relative;
}

.amount-text {
  min-width: 2rem;
  text-align: right;
}

.cursor-blink {
  width: 2px;
  height: 1.6rem;
  background: #fe9b20;
  margin-left: 0.2rem;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.balance-box {
  text-align: center;
  padding: 0.5rem 0;
}

.balance-box p {
  margin: 0;
  font-size: 0.7rem;
  color: #666;
}

.text-orange {
  color: #fe9b20 !important;
}

.right {
  float: right;
}

.left {
  float: left;
}

/* 消息提示 */
.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 16px;
  z-index: 1000;
  max-width: 80%;
  text-align: center;
}

.message-box.error {
  background: rgba(255, 59, 48, 0.9);
}

.message-box.success {
  background: rgba(52, 199, 89, 0.9);
}

/* 数字键盘 - 完全按照原版本样式 */
.keyboard {
  width: 100%;
  position: absolute;
  bottom: 0;
  background-color: #fff;
  font-size: 0;
}

.keyboard.hide {
  bottom: -13.5rem;
}

.keyboard .haojin-bear {
  position: absolute;
  z-index: -1;
  top: -3.25rem;
  width: 5.85rem;
  height: 3.25rem;
  background-size: 5.85rem 3.25rem;
}

.keyboard .haojin {
  position: absolute;
  z-index: -1;
  top: -1.5rem;
  left: 7.5rem;
  color: #8a8c92;
  font-size: 0.85rem;
  display: inline-block;
}

.keyboard .haojin .haojin-str {
  font-size: 0.75rem;
}

.keyboard .haojin .haojin-logo {
  background-image: url("data:image/png;base64,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");
  background-size: 1rem 1rem;
  width: 1rem;
  height: 1rem;
}

.keyboard .key {
  color: #4e5057;
  display: block;
  float: left;
  text-align: center;
  width: 25%;
  height: 3.4rem;
  line-height: 3.4rem;
  font-size: 1.6rem;
  border: 1px solid #e5e5e5;
  border-right: none;
  background-color: #fff;
  box-sizing: border-box;
  cursor: pointer;
}

.keyboard .key.hover {
  background-color: #ebebeb;
}

.keyboard .key.pay {
  margin: 0;
  padding-top: 3.5rem;
  height: 10.2rem;
  font-size: 0.85rem;
  line-height: 1.25rem;
  background-color: #bcbcbc;
  color: #fff;
  border: none;
  float: right;
}

.keyboard .key.pay.active {
  background-color: #fe9b20;
}

.keyboard .key.icon {
  background-size: 1.5rem 1.5rem;
  background-repeat: no-repeat;
  background-position: 50%;
}

.keyboard .key.icon.icon-backspace {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiB3aWR0aD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTAgMGg0OHY0OGgtNDh6IiBmaWxsPSJub25lIi8+PHBhdGggZmlsbD0iIzRFNTA1NyIgZD0iTTQ0IDZoLTMwYy0xLjM4IDAtMi40Ny43LTMuMTkgMS43NmwtMTAuODEgMTYuMjMgMTAuODEgMTYuMjNjLjcyIDEuMDYgMS44MSAxLjc4IDMuMTkgMS43OGgzMGMyLjIxIDAgNC0xLjc5IDQtNHYtMjhjMC0yLjIxLTEuNzktNC00LTR6bS02IDI1LjE3bC0yLjgzIDIuODMtNy4xNy03LjE3LTcuMTcgNy4xNy0yLjgzLTIuODMgNy4xNy03LjE3LTcuMTctNy4xNyAyLjgzLTIuODMgNy4xNyA3LjE3IDcuMTctNy4xNyAyLjgzIDIuODMtNy4xNyA3LjE3IDcuMTcgNy4xN3oiLz48L3N2Zz4=);
}

.keyboard .key.icon.icon-hide {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiB3aWR0aD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTAgMGg0OHY0OGgtNDh6IiBmaWxsPSJub25lIi8+PHBhdGggZmlsbD0iIzRFNTA1NyIgZD0iTTQwIDZoLTMyYy0yLjIxIDAtMy45OCAxLjc5LTMuOTggNGwtLjAyIDIwYzAgMi4yMSAxLjc5IDQgNCA0aDMyYzIuMjEgMCA0LTEuNzkgNC00di0yMGMwLTIuMjEtMS43OS00LTQtNHptLTE4IDZoNHY0aC00di00em0wIDZoNHY0aC00di00em0tNi02aDR2NGgtNHYtNHptMCA2aDR2NGgtNHYtNHptLTIgNGgtNHYtNGg0djR6bTAtNmgtNHYtNGg0djR6bTE4IDE0aC0xNnYtNGgxNnY0em0wLThoLTR2LTRoNHY0em0wLTZoLTR2LTRoNHY0em02IDZoLTR2LTRoNHY0em0wLTZoLTR2LTRoNHY0em0tMTQgMzBsOC04aC0xNmw4IDh6Ii8+PC9zdmc+);
}

/* 支付订单弹窗 */
.order-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 2000;
}

.order-container {
  background: #fff;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  width: 100%;
  max-width: 640px;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.order-header {
  text-align: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-header h3 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.order-list,
.balance-section {
  list-style: none;
  margin: 0;
  padding: 0;
}

.order-item,
.balance-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.order-item:last-child,
.balance-item:last-child {
  border-bottom: none;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-left {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
}

.item-right {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.check-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("/public/static/images/check-icon.png");
  background-size: contain;
}

.order-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}

.order-brand .brand-logo {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.pay-button {
  width: 100%;
  height: 50px;
  background: #ff7830;
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 20px;
}

.pay-button:hover {
  background: #e6691a;
}

.pay-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 响应式设计 */

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* 兼容性样式 - 与原版本保持一致 */
.key:active {
  background: #dee2e6;
  transform: scale(0.95);
}

.key.hover {
  background: #e9ecef;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .key:hover {
    background: #f8f9fa;
  }

  .key:active {
    background: #e9ecef;
  }
}

/* 防止用户选择文本 */
body {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

/* 确保在小屏幕设备上的显示效果 */
@media (max-width: 375px) {
  .shop-name {
    font-size: 18px;
  }

  .amount-display {
    font-size: 28px;
  }

  .key {
    height: 45px;
    font-size: 16px;
  }

  .pay-key {
    height: 100px;
    font-size: 14px;
  }
}
