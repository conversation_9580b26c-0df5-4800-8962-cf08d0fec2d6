// 礼品兑换页面 Vue.js 应用
const { createApp } = Vue;

createApp({
  data() {
    return {
      loading: true,
      pageData: {
        banner_list: [],
        pick_up_type: 1,
        code_field_alias: "卡号",
        password_field_alias: "密码",
        page_type: "",
        allow_submit_order: 1,
        pick_up_notice: "",
        pick_up_notice_pop: "",
        show_scan_button: 1,
        code_is_all_number: 0,
        pickup_background_color: "",
        pickup_background_image_url: "",
      },
      formData: {
        code: "",
        password: "",
        phone: "",
        verify_code: "",
      },
      // Tab页状态
      currentTab: 0, // 0: 兑换码模式, 1: 手机号模式
      // 验证码相关
      smsCountdown: 0,
      smsTimer: null,
      smsLoading: false, // 发送验证码loading状态
      // 提交状态
      submitting: false,
      // 页面参数
      bid: "",
      page_type: "",
      auto: "",
      code: "",
      password: "",
      coupon_guid: "",
      goods_guid: "",
    };
  },

  computed: {
    isCodeNumeric() {
      return this.pageData.code_is_all_number == 1;
    },

    canSubmit() {
      return this.pageData.allow_submit_order == 1 && !this.submitting;
    },

    submitButtonText() {
      if (this.submitting) return "提交中...";
      return this.pageData.allow_submit_order == 1 ? "立即提交" : "提货通道未开启";
    },

    submitButtonClass() {
      return this.canSubmit ? "layui-btn layui-btn-fluid" : "layui-btn layui-btn-fluid layui-btn-disabled";
    },

    smsButtonText() {
      if (this.smsLoading) return "发送中...";
      return this.smsCountdown > 0 ? `${this.smsCountdown}秒` : "发送";
    },

    smsButtonDisabled() {
      return this.smsCountdown > 0 || this.smsLoading;
    },

    // 当前激活的提交模式
    currentPickupType() {
      if (this.pageData.pick_up_type == 3) {
        return this.currentTab == 0 ? 1 : 2;
      }
      return this.pageData.pick_up_type;
    },
  },

  mounted() {
    this.initPage();
    this.loadPageData();
  },

  methods: {
    initPage() {
      // 获取URL参数
      this.bid = this.getQueryString("bid");
      this.page_type = this.getQueryString("type");
      this.auto = this.getQueryString("a");
      this.code = this.getQueryString("c");
      this.password = this.getQueryString("p");
      this.coupon_guid = this.getQueryString("coupon_guid");
      this.goods_guid = this.getQueryString("goods_guid");

      // 处理coupon_guid缓存
      if (this.coupon_guid) {
        sessionStorage.setItem("coupon_guid", this.coupon_guid);
      } else {
        this.coupon_guid = sessionStorage.getItem("coupon_guid");
      }

      // 预填充表单数据
      if (this.code) {
        this.formData.code = this.code;
      }
      if (this.password) {
        this.formData.password = this.password;
      }
    },

    loadPageData() {
      const params = {
        code: this.code,
        coupon_guid: this.coupon_guid,
      };

      post_layui_member_api_v1(
        "/code/get_config",
        params,
        (result) => {
          this.pageData = { ...this.pageData, ...result.data };
          this.pageData.page_type = this.page_type;

          document.title = result.data.title;
          this.loading = false;

          this.$nextTick(() => {
            this.initLayuiForm();
            this.applyTheme();
            this.showNoticePopup();
            this.checkWeixinEnvironment();
            this.autoSubmit();
            // 延迟初始化轮播图，确保DOM完全渲染
            setTimeout(() => {
              this.initSwiper();
            }, 100);
          });
        },
        () => {
          layer.msg("加载失败，请重试");
          this.loading = false;
        }
      );
    },

    initLayuiForm() {
      // 初始化LayUI form和element组件
      layui.use(["form", "element"], (form, element) => {
        // 重新渲染表单
        form.render();

        // 监听表单提交 - 兑换码模式
        form.on("submit(codeForm)", () => {
          this.handleSubmit(1); // 传入模式类型
          return false; // 阻止默认提交
        });

        // 监听表单提交 - 手机号模式
        form.on("submit(phoneForm)", () => {
          this.handleSubmit(2); // 传入模式类型
          return false; // 阻止默认提交
        });

        // 监听Tab切换
        if (this.pageData.pick_up_type == 3) {
          element.on("tab(pickupTabs)", (data) => {
            this.currentTab = data.index;
            console.log("切换到Tab:", data.index);
            // 切换Tab时重新渲染表单
            this.$nextTick(() => {
              form.render();
            });
          });
        }
      });
    },

    initSwiper() {
      if (this.pageData.banner_list && this.pageData.banner_list.length > 0) {
        // 确保DOM已经渲染完成
        this.$nextTick(() => {
          const swiperEl = document.querySelector(".swiper");
          if (swiperEl && typeof Swiper !== "undefined") {
            const swiper = new Swiper(".swiper", {
              direction: "horizontal",
              loop: this.pageData.banner_list.length > 1,
              autoHeight: true,
              autoplay:
                this.pageData.banner_list.length > 1
                  ? {
                      delay: 3000,
                      disableOnInteraction: false,
                    }
                  : false,
              speed: 1000,
              pagination: {
                el: ".swiper-pagination",
                clickable: true,
              },
            });

            swiper.on("slideChange", function () {
              console.log("slide changed");
            });
          }
        });
      }
    },

    applyTheme() {
      const { pickup_background_color, pickup_background_image_url } = this.pageData;

      if (pickup_background_color) {
        document.body.style.backgroundColor = pickup_background_color;
      }

      if (pickup_background_image_url) {
        document.body.style.background = `url(${pickup_background_image_url}) center center / 100% 100% no-repeat`;
        document.body.style.overflow = "hidden";
        document.body.style.height = "844px";

        // 设置版权信息文字颜色
        const copyRightName = document.getElementById("copy_right_name");
        const copyRightFooter = document.getElementById("copy_right_footer");

        if (copyRightName) copyRightName.style.color = "white";
        if (copyRightFooter) copyRightFooter.style.color = "white";

        // 把版权置底
        const copyRight = document.getElementById("copy_right");
        if (copyRight) copyRight.className = "empty_notice";
      }
    },

    // 刷新layer弹窗的居中位置 - 平滑过渡版
    refreshLayerCenter(index, smooth = true) {
      if (!index) return;

      const layero = $("#layui-layer" + index);
      if (!layero.length) return;

      const winWidth = $(window).width();
      const winHeight = $(window).height();
      const layerWidth = layero.outerWidth();
      const layerHeight = layero.outerHeight();

      // 计算居中位置
      let left = (winWidth - layerWidth) / 2;
      let top = (winHeight - layerHeight) / 2;

      // 防止超出屏幕边界
      left = Math.max(0, left);
      top = Math.max(0, top);

      if (smooth) {
        // 添加平滑过渡效果
        layero.css({
          'transition': 'left 0.3s ease-out, top 0.3s ease-out',
          'left': left + 'px',
          'top': top + 'px'
        });

        // 过渡完成后移除transition，避免影响其他操作
        setTimeout(() => {
          layero.css('transition', '');
        }, 300);
      } else {
        // 直接设置位置，无过渡
        layer.style(index, {
          left: left + 'px',
          top: top + 'px'
        });
      }
    },

    showNoticePopup() {
      let self = this;
      if (this.pageData.pick_up_notice_pop) {
        // 检查是否已经显示过弹窗公告
          layer.open({
            type: 1,
            title: false,
            area: ['80%', 'auto'], // 宽度80%，高度自适应
            maxHeight: window.innerHeight * 0.8, // 最大高度为屏幕的80%
            closeBtn: 1,
            shadeClose: true,
            scrollbar: false, // 禁止页面滚动条
            content: `
              <div class="notice-popup-content" style="display: flex; flex-direction: column; min-height: 200px;">
                <div class="notice-popup-title" style="flex-shrink: 0; text-align: center; padding:0;">
                  <h2 style="margin: 0; color: #333;">温馨提示</h2>
                </div>
                <div class="notice-popup-body" style="flex: 1; overflow-y: auto; padding: 0;">
                  ${this.pageData.pick_up_notice_pop}
                </div>
                <div class="notice-popup-footer" style="flex-shrink: 0; text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
                  <button onclick="layer.closeAll();" type="button" class="layui-btn layui-btn-primary layui-border-green">我知道了</button>
                </div>
              </div>
            `,
            success: function(layero, index) {

              // 确保图片样式正确
              const images = layero.find('img');
              if (images.length > 0) {
                // 如果有图片，先降低透明度，避免闪动
                layero.css('opacity', '0.7');

                images.css({
                  'max-width': '100%',
                  'height': 'auto',
                  'display': 'block',
                  'margin': '10px auto'
                });

                // 图片加载完成后重新居中
                let loadedCount = 0;
                const totalImages = images.length;

                const handleImageLoaded = () => {
                  loadedCount++;
                  if (loadedCount === totalImages) {
                    // 所有图片加载完成，重新居中并恢复透明度
                    setTimeout(() => {
                      self.refreshLayerCenter(index, true);
                      // 恢复透明度，添加淡入效果
                      layero.css({
                        'transition': 'opacity 0.2s ease-in',
                        'opacity': '1'
                      });
                      setTimeout(() => layero.css('transition', ''), 200);
                    }, 50);
                  }
                };

                images.each(function() {
                  const img = $(this);
                  if (this.complete) {
                    handleImageLoaded();
                  } else {
                    img.on('load', handleImageLoaded);
                    // 处理图片加载失败的情况
                    img.on('error', handleImageLoaded);
                  }
                });
              }
            }
          });
      }
    },

    handleSubmit(submitType = null) {
      if (!this.canSubmit || this.submitting) return;

      this.submitting = true;
      this.setButtonLoading("submit", true);

      if (this.page_type === "query") {
        // 查询模式
        this.redirect(`detail.html?bid=${this.bid}&c=${this.formData.code}`);
        this.submitting = false;
        this.setButtonLoading("submit", false);
        return;
      }

      // 确定提交类型
      let actualPickupType = this.pageData.pick_up_type;
      if (this.pageData.pick_up_type == 3) {
        // 两种模式都支持时，根据当前Tab或传入的类型确定
        actualPickupType = submitType || (this.currentTab == 0 ? 1 : 2);
      }

      const submitData = {
        ...this.formData,
        pick_up_type: actualPickupType,
      };

      if (this.coupon_guid) {
        submitData.coupon_guid = this.coupon_guid;
      }
      if (this.goods_guid) {
        submitData.goods_guid = this.goods_guid;
      }

      post_layui_member_api_v1(
        "/code/verify_code",
        submitData,
        (result) => {
          this.submitting = false;
          this.setButtonLoading("submit", false);
          console.log(result);
          if (result.data.url) {
            this.redirect(result.data.url);
          } else if (result.data.status == 1) {
            this.redirect(`/member/code/detail.html?bid=${this.bid}&token=${result.data.data.token}`);
          } else if (result.data.status == 2) {
            this.redirect(`/member/goods_order/detail?bid=${this.bid}&order_guid=${result.data.data.order_guid}`);
          } else if (result.data.status == 3) {
            this.redirectSubmitOrder(result.data.data.token, result.data.data.goods_guid);
          }
        },
        (error) => {
          this.submitting = false;
          this.setButtonLoading("submit", false);
          layer.msg(error.msg || "提交失败，请重试");
        }
      );
    },

    redirectSubmitOrder(token, goods_guid) {
      const wsCache = new WebStorageCache();
      let choose_goods_info = wsCache.get("choose_goods_info");

      if (!choose_goods_info) {
        choose_goods_info = [
          {
            guid: goods_guid,
            attr: [],
            amount: 1,
          },
        ];
        wsCache.set("choose_goods_info", choose_goods_info, { exp: 3600 });
      }

      this.redirect(`submit_order.html?bid=${this.bid}&token=${token}`);
    },

    handleScan() {
      if (!is_weixin()) {
        layer.msg("请在微信中使用扫码功能");
        return;
      }

      wx.scanQRCode({
        needResult: 1,
        desc: "扫描兑换码",
        success: (res) => {
          console.log(res);
          if (res.errMsg === "scanQRCode:ok") {
            post_layui_member_api_v1(
              "/code/parse_qrcode",
              { text: res.resultStr },
              (result) => {
                console.log(result);
                if (result.data.code) {
                  this.formData.code = result.data.code.replace("CODE_128,", "");
                  layer.msg("扫码成功");
                }
                if (result.data.password) {
                  this.formData.password = result.data.password;
                }
                // 扫码成功后自动聚焦密码输入框
                if (this.pageData.page_type !== "query") {
                  this.$nextTick(() => {
                    const passwordInput = document.querySelector('input[name="password"]');
                    if (passwordInput) passwordInput.focus();
                  });
                }
              },
              () => {
                layer.msg("二维码解析失败");
              }
            );
          } else {
            layer.msg("扫码失败：" + res.errMsg);
          }
        },
        error: (res) => {
          layer.msg("扫码出错：" + res);
        },
      });
    },

    sendSmsCode() {
      if (this.smsButtonDisabled) return;

      // 手动验证手机号
      if (!this.formData.phone || this.formData.phone.trim() === "") {
        layer.msg("请输入手机号");
        // 聚焦到手机号输入框
        this.$nextTick(() => {
          const phoneInput = document.querySelector('input[name="phone"]');
          if (phoneInput) phoneInput.focus();
        });
        return;
      }

      // 验证手机号格式
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!phoneReg.test(this.formData.phone)) {
        layer.msg("请输入正确的手机号");
        // 聚焦到手机号输入框
        this.$nextTick(() => {
          const phoneInput = document.querySelector('input[name="phone"]');
          if (phoneInput) phoneInput.focus();
        });
        return;
      }

      // 开启loading状态
      this.smsLoading = true;
      this.setButtonLoading("sms", true);

      // 验证通过，发送验证码
      post_layui_member_api_v1(
        "/user/send_sms_code",
        { mobile: this.formData.phone },
        (result) => {
          this.smsLoading = false;
          this.setButtonLoading("sms", false);
          layer.msg(result.msg);
          this.startSmsCountdown();
          // 发送成功后自动聚焦验证码输入框
          this.$nextTick(() => {
            const verifyInput = document.querySelector('input[name="verify_code"]');
            if (verifyInput) verifyInput.focus();
          });
        },
        (error) => {
          this.smsLoading = false;
          this.setButtonLoading("sms", false);
          layer.msg(error.msg || "发送失败，请重试");
        }
      );
    },

    startSmsCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer);
          this.smsTimer = null;
        }
      }, 1000);
    },

    checkWeixinEnvironment() {
      // 检查是否在微信环境中，如果不是则隐藏扫码按钮
      if (typeof is_weixin === "function" && !is_weixin()) {
        this.pageData.show_scan_button = 0;
      }
    },

    autoSubmit() {
      if (this.code && this.password && this.auto) {
        this.handleSubmit();
      }
    },

    // 设置按钮loading效果
    setButtonLoading(type, loading) {
      this.$nextTick(() => {
        let buttons = [];

        if (type === "submit") {
          // 获取所有提交按钮
          buttons = document.querySelectorAll('button[lay-submit][lay-filter="codeForm"], button[lay-submit][lay-filter="phoneForm"]');
        } else if (type === "sms") {
          // 获取所有发送验证码按钮
          buttons = document.querySelectorAll(".code-input-container button");
        }

        buttons.forEach((button) => {
          if (loading) {
            // 添加loading效果
            button.classList.add("layui-btn-loading");

            if (type === "submit") {
              // 处理提交按钮
              const originalIcon = button.querySelector("i:not(.loading-icon)");
              if (originalIcon) {
                // 隐藏原始图标
                originalIcon.style.display = "none";
                originalIcon.setAttribute("data-original-class", originalIcon.className);
              }

              // 添加loading图标
              let loadingIcon = button.querySelector("i.loading-icon");
              if (!loadingIcon) {
                loadingIcon = document.createElement("i");
                loadingIcon.className = "layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop loading-icon";
                button.insertBefore(loadingIcon, button.firstChild);
              }
            } else if (type === "sms") {
              // 处理发送验证码按钮
              // 添加loading图标
              let loadingIcon = button.querySelector("i.loading-icon");
              if (!loadingIcon) {
                loadingIcon = document.createElement("i");
                loadingIcon.className = "layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop loading-icon";
                button.insertBefore(loadingIcon, button.firstChild);
              }
            }
          } else {
            // 移除loading效果
            button.classList.remove("layui-btn-loading");

            if (type === "submit") {
              // 处理提交按钮
              const loadingIcon = button.querySelector("i.loading-icon");
              if (loadingIcon) {
                loadingIcon.remove();
              }

              const originalIcon = button.querySelector("i:not(.loading-icon)");
              if (originalIcon) {
                // 恢复原始图标
                originalIcon.style.display = "";
                const originalClass = originalIcon.getAttribute("data-original-class");
                if (originalClass) {
                  originalIcon.className = originalClass;
                  originalIcon.removeAttribute("data-original-class");
                }
              }
            } else if (type === "sms") {
              // 处理发送验证码按钮
              const loadingIcon = button.querySelector("i.loading-icon");
              if (loadingIcon) {
                loadingIcon.remove();
              }
            }
          }
        });
      });
    },

    // 工具方法
    getQueryString(name) {
      return getQueryString(name);
    },

    redirect(url) {
      redirect(url);
    },
  },

  watch: {
    // 监听pageData变化，重新渲染表单
    "pageData.pick_up_type"() {
      this.$nextTick(() => {
        this.initLayuiForm();
      });
    },

    // 监听Tab切换，清空对应表单数据
    currentTab(newTab) {
      if (newTab === 0) {
        // 切换到兑换码模式，清空手机号相关数据
        this.formData.phone = "";
        this.formData.verify_code = "";
      } else {
        // 切换到手机号模式，清空兑换码相关数据
        this.formData.code = "";
        this.formData.password = "";
      }
    },
  },

  beforeUnmount() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer);
    }
  },
}).mount("#app");
