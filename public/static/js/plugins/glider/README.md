# Glider.js 中文使用文档

## 简介

Glider.js 是一个超轻量级的轮播图/滑块组件，仅有 2.8KB (gzipped)，无任何依赖，支持响应式设计和移动端触摸操作。

### 特点
- ✅ 超轻量级 (2.8KB gzipped)
- ✅ 零依赖，纯原生 JavaScript
- ✅ 完全响应式设计
- ✅ 支持触摸和鼠标拖拽
- ✅ 支持键盘导航
- ✅ 原生滚动性能
- ✅ 可访问性友好

## 快速开始

### 1. 引入文件

```html
<!-- CSS 样式文件 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glider-js@1/glider.min.css">

<!-- JavaScript 文件 -->
<script src="https://cdn.jsdelivr.net/npm/glider-js@1/glider.min.js"></script>
```

### 2. HTML 结构

```html
<div class="glider-contain">
  <!-- 轮播容器 -->
  <div class="glider">
    <div class="slide">幻灯片 1</div>
    <div class="slide">幻灯片 2</div>
    <div class="slide">幻灯片 3</div>
    <div class="slide">幻灯片 4</div>
  </div>

  <!-- 左右箭头按钮 (可选) -->
  <button aria-label="上一张" class="glider-prev">‹</button>
  <button aria-label="下一张" class="glider-next">›</button>

  <!-- 指示点 (可选) -->
  <div role="tablist" class="dots"></div>
</div>
```

### 3. 基础初始化

```javascript
// 最简单的用法
new Glider(document.querySelector('.glider'));

// 带配置的用法
new Glider(document.querySelector('.glider'), {
  slidesToShow: 1,        // 显示幻灯片数量
  slidesToScroll: 1,      // 每次滚动数量
  dots: '.dots',          // 指示点容器
  arrows: {               // 箭头按钮
    prev: '.glider-prev',
    next: '.glider-next'
  }
});
```

## 配置选项

### 基础配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `slidesToShow` | Number/Object | 1 | 显示的幻灯片数量 |
| `slidesToScroll` | Number | 1 | 每次滚动的幻灯片数量 |
| `itemWidth` | Number | undefined | 每个幻灯片的宽度(px) |
| `exactWidth` | Boolean | false | 是否使用精确宽度 |
| `resizeLock` | Boolean | true | 窗口大小改变时是否锁定 |
| `draggable` | Boolean | true | 是否可拖拽 |
| `dragVelocity` | Number | 3.3 | 拖拽速度系数 |
| `dots` | String/Element | undefined | 指示点容器选择器 |
| `arrows` | Object | undefined | 箭头按钮配置 |

### 高级配置

```javascript
new Glider(document.querySelector('.glider'), {
  // 显示配置
  slidesToShow: 3,
  slidesToScroll: 1,
  
  // 拖拽配置
  draggable: true,
  dragVelocity: 3.3,
  
  // 滚动配置
  scrollLock: true,
  scrollLockDelay: 250,
  
  // 循环播放
  rewind: true,
  
  // 动画时长
  duration: 0.5,
  
  // 跳过轨道
  skipTrack: true,
  
  // 响应式配置
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2
      }
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3
      }
    }
  ]
});
```

## 响应式设计

```javascript
new Glider(document.querySelector('.glider'), {
  slidesToShow: 1,
  slidesToScroll: 1,
  draggable: true,
  dots: '.dots',
  arrows: {
    prev: '.glider-prev',
    next: '.glider-next'
  },
  responsive: [
    {
      // 平板设备
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
        itemWidth: 150,
        duration: 0.25
      }
    },
    {
      // 桌面设备
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 2,
        itemWidth: 200,
        duration: 0.25
      }
    }
  ]
});
```

## API 方法

### 实例方法

```javascript
const glider = new Glider(document.querySelector('.glider'));

// 跳转到指定幻灯片 (从0开始)
glider.scrollItem(2);

// 刷新轮播图
glider.refresh();

// 销毁实例
glider.destroy();

// 添加幻灯片
glider.addItem('<div>新幻灯片</div>');

// 移除幻灯片 (从0开始)
glider.removeItem(0);

// 设置选项
glider.setOption({
  slidesToShow: 2,
  slidesToScroll: 1
});
```

## 事件监听

### 可用事件

```javascript
const glider = new Glider(document.querySelector('.glider'));

// 幻灯片显示时触发
glider.ele.addEventListener('glider-slide-visible', function(event) {
  console.log('显示幻灯片:', event.detail.slide);
});

// 幻灯片隐藏时触发
glider.ele.addEventListener('glider-slide-hidden', function(event) {
  console.log('隐藏幻灯片:', event.detail.slide);
});

// 轮播图刷新时触发
glider.ele.addEventListener('glider-refresh', function(event) {
  console.log('轮播图已刷新');
});

// 动画完成时触发
glider.ele.addEventListener('glider-animated', function(event) {
  console.log('动画完成');
});

// 加载完成时触发
glider.ele.addEventListener('glider-loaded', function(event) {
  console.log('轮播图加载完成');
});
```

## 实用示例

### 1. 图片轮播

```html
<div class="glider-contain">
  <div class="glider">
    <div><img src="image1.jpg" alt="图片1"></div>
    <div><img src="image2.jpg" alt="图片2"></div>
    <div><img src="image3.jpg" alt="图片3"></div>
  </div>
  <button class="glider-prev">‹</button>
  <button class="glider-next">›</button>
  <div class="dots"></div>
</div>

<script>
new Glider(document.querySelector('.glider'), {
  slidesToShow: 1,
  dots: '.dots',
  arrows: {
    prev: '.glider-prev',
    next: '.glider-next'
  },
  draggable: true,
  scrollLock: true
});
</script>
```

### 2. 产品展示

```javascript
new Glider(document.querySelector('.product-slider'), {
  slidesToShow: 4,
  slidesToScroll: 2,
  draggable: true,
  dots: '.product-dots',
  arrows: {
    prev: '.product-prev',
    next: '.product-next'
  },
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }
  ]
});
```

### 3. 懒加载图片

```javascript
const glider = new Glider(document.querySelector('.glider'), {
  slidesToShow: 1,
  dots: '.dots',
  arrows: {
    prev: '.glider-prev',
    next: '.glider-next'
  }
});

// 懒加载实现
glider.ele.addEventListener('glider-slide-visible', function(event) {
  const slide = event.detail.slide;
  const img = slide.querySelector('img[data-src]');
  if (img) {
    img.src = img.dataset.src;
    img.removeAttribute('data-src');
    img.classList.add('loaded');
  }
});
```

## CSS 样式定制

### 基础样式

```css
.glider-contain {
  width: 100%;
  position: relative;
}

.glider {
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.glider-track {
  transform: translateZ(0);
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  z-index: 1;
}

.glider > * {
  user-select: none;
  outline: none;
  flex: 0 0 auto;
}
```

### 箭头按钮样式

```css
.glider-prev,
.glider-next {
  position: absolute;
  top: 50%;
  z-index: 2;
  color: #666;
  text-decoration: none;
  border: 0;
  padding: 16px;
  margin: 0;
  font-size: 18px;
  cursor: pointer;
  font-weight: 900;
  outline: none;
  background: rgba(255,255,255,0.8);
  border-radius: 4px;
  transform: translateY(-50%);
}

.glider-prev {
  left: 25px;
}

.glider-next {
  right: 25px;
}

.glider-prev:hover,
.glider-next:hover {
  color: #a89cc8;
  background: white;
}
```

### 指示点样式

```css
.dots {
  text-align: center;
  margin-top: 20px;
}

.glider-dot {
  border: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
  margin: 0 6px;
  cursor: pointer;
  outline: none;
}

.glider-dot.active {
  background: #a89cc8;
}
```

## 常见问题

### Q: 如何实现自动播放？
A: Glider.js 本身不支持自动播放，需要结合 `setInterval` 实现：

```javascript
const glider = new Glider(document.querySelector('.glider'), {
  slidesToShow: 1,
  slidesToScroll: 1,
  dots: '.dots',
  arrows: {
    prev: '.glider-prev',
    next: '.glider-next'
  }
});

// 自动播放
setInterval(() => {
  if (glider.slide >= glider.slides.length - glider.opt.slidesToShow) {
    glider.scrollItem(0); // 回到第一张
  } else {
    glider.scrollItem(glider.slide + 1);
  }
}, 3000);
```

### Q: 如何动态添加/删除幻灯片？
A: 使用 `addItem()` 和 `removeItem()` 方法：

```javascript
// 添加幻灯片
glider.addItem('<div>新内容</div>');

// 删除第一张幻灯片
glider.removeItem(0);

// 刷新轮播图
glider.refresh();
```

### Q: 如何获取当前显示的幻灯片？
A: 通过 `slide` 属性获取：

```javascript
console.log('当前幻灯片索引:', glider.slide);
```

## 浏览器兼容性

- Chrome/Safari/Firefox/Edge (现代浏览器)
- IE 10+ (需要 polyfill)
- iOS Safari 8+
- Android Browser 4.4+

## 许可证

MIT License

---

更多信息请访问：[Glider.js 官方网站](https://nickpiscitelli.github.io/Glider.js/)
