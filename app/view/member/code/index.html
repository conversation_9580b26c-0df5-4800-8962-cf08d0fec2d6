{layout name="layui"/}
<!-- 引入页面专用样式文件 -->
<link href="/static/css/member/code/index.css?v=1" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" href="/static/css/member/code/swiper-bundle.min.css?v=11.1.14"/>

<!-- Vue应用容器 -->
<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-state">
    <div class="loading-content">
      <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      <h4>加载中...</h4>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else>
    <!-- 轮播图 -->
    <div v-if="pageData.banner_list && pageData.banner_list.length > 0" class="swiper">
      <div class="swiper-wrapper">
        <div v-for="(banner, index) in pageData.banner_list" :key="index" class="swiper-slide">
          <img class="pic img-item slide-image" :src="banner.img_url"/>
        </div>
      </div>
      <div class="swiper-pagination"></div>
    </div>

    <div class="main-container">
      <!-- 单一模式表单 -->
      <form v-if="pageData.pick_up_type == 1 || pageData.pick_up_type == 2" class="layui-form layui-form-pane"
            @submit.prevent="handleSubmit">
        <!-- 兑换码模式 -->
        <template v-if="pageData.pick_up_type == 1">
          <div class="layui-form-item">
            <label class="layui-form-label" required>{{ pageData.code_field_alias }}</label>
            <div class="layui-input-block">
              <input
                      :type="isCodeNumeric ? 'tel' : 'text'"
                      v-model="formData.code"
                      name="code"
                      lay-verify="required"
                      :lay-reqtext="`请输入${pageData.code_field_alias}`"
                      :placeholder="`请输入${pageData.code_field_alias}`"
                      autocomplete="off"
                      class="layui-input"
              />
              <img v-if="pageData.show_scan_button == 1 && !loading" class="scan_qrcode scan-image"
                   src="/static/img/scan.png" @click="handleScan" style="cursor: pointer"/>
            </div>
          </div>

          <div v-if="pageData.page_type != 'query'" class="layui-form-item">
            <label class="layui-form-label" required>{{ pageData.password_field_alias }}</label>
            <div class="layui-input-block">
              <input
                      type="password"
                      v-model="formData.password"
                      name="password"
                      lay-verify="required"
                      :lay-reqtext="`请输入${pageData.password_field_alias}`"
                      :placeholder="`请输入${pageData.password_field_alias}`"
                      autocomplete="off"
                      class="layui-input"
              />
            </div>
          </div>
        </template>

        <!-- 手机号模式 -->
        <template v-if="pageData.pick_up_type == 2">
          <div class="layui-form-item">
            <label class="layui-form-label" required>手机号</label>
            <div class="layui-input-block">
              <input type="tel" maxlength="11" v-model="formData.phone" name="phone" lay-verify="required|phone"
                     lay-reqtext="请输入手机号" placeholder="请输入手机号" class="layui-input"/>
            </div>
          </div>

          <div class="layui-form-item">
            <label class="layui-form-label" required>验证码</label>
            <div class="layui-input-block">
              <div class="layui-col-xs7">
                <input type="tel" maxlength="6" v-model="formData.verify_code" name="verify_code"
                       lay-verify="required|number" lay-reqtext="请输入验证码" placeholder="验证码"
                       class="layui-input"/>
              </div>
              <div class="layui-col-xs5">
                <div class="code-input-container">
                  <button type="button" @click="sendSmsCode" :disabled="smsButtonDisabled"
                          class="layui-btn layui-btn-primary layui-btn-fluid">{{ smsButtonText }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </template>

        <div class="layui-form-item">
          <button :class="submitButtonClass" :disabled="!canSubmit" lay-submit lay-filter="codeForm">
            <i class="layui-icon">&#xe609;</i>
            {{ submitButtonText }}
          </button>
        </div>
      </form>

      <!-- 两种模式都支持时使用Tab页 -->
      <div v-if="pageData.pick_up_type == 3" class="layui-tab layui-tab-brief" lay-filter="pickupTabs">
        <ul class="layui-tab-title">
          <li class="layui-this">{{ pageData.code_field_alias || '兑换码' }}兑换</li>
          <li>手机号兑换</li>
        </ul>
        <div class="layui-tab-content">
          <!-- 兑换码模式Tab -->
          <div class="layui-tab-item layui-show">
            <form class="layui-form layui-form-pane" @submit.prevent="handleSubmit">
              <div class="layui-form-item">
                <label class="layui-form-label" required>{{ pageData.code_field_alias }}</label>
                <div class="layui-input-block">
                  <input
                          :type="isCodeNumeric ? 'tel' : 'text'"
                          v-model="formData.code"
                          name="code"
                          lay-verify="required"
                          :lay-reqtext="`请输入${pageData.code_field_alias}`"
                          :placeholder="`请输入${pageData.code_field_alias}`"
                          autocomplete="off"
                          class="layui-input"
                  />
                  <img v-if="pageData.show_scan_button == 1 && !loading" class="scan_qrcode scan-image"
                       src="/static/img/scan.png" @click="handleScan" style="cursor: pointer"/>
                </div>
              </div>

              <div v-if="pageData.page_type != 'query'" class="layui-form-item">
                <label class="layui-form-label" required>{{ pageData.password_field_alias }}</label>
                <div class="layui-input-block">
                  <input
                          type="password"
                          v-model="formData.password"
                          name="password"
                          lay-verify="required"
                          :lay-reqtext="`请输入${pageData.password_field_alias}`"
                          :placeholder="`请输入${pageData.password_field_alias}`"
                          autocomplete="off"
                          class="layui-input"
                  />
                </div>
              </div>

              <div class="layui-form-item">
                <button :class="submitButtonClass" :disabled="!canSubmit" lay-submit lay-filter="codeForm">
                  <i class="layui-icon">&#xe609;</i>
                  {{ submitButtonText }}
                </button>
              </div>
            </form>
          </div>

          <!-- 手机号模式Tab -->
          <div class="layui-tab-item">
            <form class="layui-form layui-form-pane" @submit.prevent="handleSubmit">
              <div class="layui-form-item">
                <label class="layui-form-label" required>手机号</label>
                <div class="layui-input-block">
                  <input type="tel" maxlength="11" v-model="formData.phone" name="phone" lay-verify="required|phone"
                         lay-reqtext="请输入手机号" placeholder="请输入手机号" class="layui-input"/>
                </div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label" required>验证码</label>
                <div class="layui-input-block">
                  <div class="layui-col-xs7">
                    <input
                            type="tel"
                            maxlength="6"
                            v-model="formData.verify_code"
                            name="verify_code"
                            lay-verify="required|number"
                            lay-reqtext="请输入验证码"
                            placeholder="验证码"
                            class="layui-input"
                    />
                  </div>
                  <div class="layui-col-xs5">
                    <div class="code-input-container">
                      <button type="button" @click="sendSmsCode" :disabled="smsButtonDisabled"
                              class="layui-btn layui-btn-primary layui-btn-fluid">{{ smsButtonText }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-form-item">
                <button :class="submitButtonClass" :disabled="!canSubmit" lay-submit lay-filter="phoneForm">
                  <i class="layui-icon">&#xe609;</i>
                  {{ submitButtonText }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 公告 -->
      <fieldset v-if="pageData.pick_up_notice" class="layui-elem-field">
        <legend>公告</legend>
        <div class="layui-field-box">
          <div v-html="pageData.pick_up_notice"></div>
        </div>
      </fieldset>
    </div>
  </div>
</div>

<div id="copy_right" class="has_notice">{include file="copy_right" /}</div>

<script src="/static/js/member/code/swiper-bundle.min.js?v=11.1.14"></script>
<!-- 引入页面专用JavaScript文件 -->
<script src="/static/js/member/code/index.js?v=2"></script>

{include file="code/footer" /} {include file="code/service" /} {include file="code/kefu" /}
