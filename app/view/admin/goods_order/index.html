{layout name="layui_plus"  /}
<style>
    .layui-btn-container .layui-btn {
        margin-bottom: 0;
    }

    @media screen  and (max-width: 768px) {
        .layui-table-fixed-r {
            display: none;
        }
    }
</style>
<div class='layui-fluid'>
  <div class='layui-card'>
    <div class='layui-card-body'>
      <div class="layui-tab layui-tab-brief" lay-filter="order_list">
        <ul class="layui-tab-title">
          <li class="layui-this">全部订单</li>
          <li class="" lay-id="0" data-status="0">待发货</li>
          <li class="" lay-id="1" data-status="1">已发货</li>
          <li class="" lay-id="2" data-status="2">已完成</li>
          <li class="" lay-id="3" data-status="-3">已退款</li>
          <li class="" data-time_after_day="0">今天待发</li>
          <li class="" data-time_after_day="1">明天待发</li>
          <li class="" data-time_after_day="2">后天待发</li>
        </ul>
        <div class="layui-tab-content">
          <div class="layui-tab-item layui-show"></div>
          <div class="layui-tab-item"></div> <!-- 待发货 -->
          <div class="layui-tab-item"></div> <!-- 已发货 -->
          <div class="layui-tab-item"></div> <!-- 已完成 -->
          <div class="layui-tab-item"></div> <!-- 已退款 -->
          <div class="layui-tab-item"></div> <!-- 今天待发 -->
          <div class="layui-tab-item"></div> <!-- 明天待发 -->
          <div class="layui-tab-item"></div> <!-- 后天待发 -->
        </div>
        <div class="layui-tab-item layui-show">
          <form class="layui-form" id="query_form" style="margin-left: 10px">
            <div class="layui-form-item">
              <input type="hidden" name="status" class="layui-input"/>
              <div class="layui-inline">
                <select name="key" lay-verify="">
                  <option value="bill_number_or_code">订单号/卡号</option>
                  <option value="bill_number">订单号</option>
                  <option value="code">卡号</option>
                  <option value="mobile">手机号</option>
                  <option value="m|id">会员编号</option>
                  <option value="goods_info">商品名称</option>
                  <option value="express_no">快递单号</option>
                  <option value="express_no_2" auth_admin="multiple_express_send_out_goods">
                    快递单号2
                  </option>
                  <option value="send_out_goods_remark">
                    发货备注
                  </option>
                  <option value="send_out_goods_remark_2"
                          auth_admin="multiple_express_send_out_goods">
                    发货备注2
                  </option>
                  <option value="true_name">姓名</option>
                  <option value="address">地址</option>
                  <option value="third_order_number">支付单号</option>
                  <option value="extend_field_1" auth_admin="extend_field/index">
                    字段1
                  </option>
                  <option value="extend_field_2" auth_admin="extend_field/index">
                    字段2
                  </option>
                  <option value="extend_field_3" auth_admin="extend_field/index">
                    字段3
                  </option>
                </select>
              </div>

              <div class="layui-inline">
                <input type="text" name="value" placeholder="请输入" autocomplete="off"
                       class="layui-input"/>
              </div>

              <div class="layui-inline" auth_admin="mall">
                <select name="way" lay-verify="required" lay-search="">
                  <option value="">来源</option>
                  <option value="1">提货卡</option>
                  <option value="2">商城</option>
                </select>
              </div>

              <div class="layui-inline" auth_admin="goods_order/print_express_order">
                <select name="print_status" lay-verify="required" lay-search="">
                  <option value="">打单状态</option>
                  <option value="0">未打单</option>
                  <option value="1">已打单</option>
                </select>
              </div>

              <div class="layui-inline">
                <select placeholder="卡券" lay-search name="coupon_guid" lay-verify="required"
                        lay-verType="tips" class="fsSelect" dict="coupon" addNull="1">
                </select>
              </div>
              <div class="layui-inline">
                <select placeholder='卡券归属' lay-search name="user_id" lay-verify="required"
                        lay-verType="tips"
                        class="fsSelect" dict="user" addNull="1">
                </select>
              </div>
              <div class="layui-inline">
                <select placeholder='订单归属' lay-search name="go|owner_user_id" lay-verify="required"
                        lay-verType="tips"
                        class="fsSelect" dict="user" addNull="1">
                </select>
              </div>

              <div class="layui-inline">
                <select placeholder='发货人' lay-search name="send_or_pick_up_user_guid"
                        lay-verify="required"
                        lay-verType="tips"
                        class="fsSelect" dict="user_guid" addNull="1">
                </select>
              </div>

              <div class="layui-inline" auth_admin="store/index">
                <select placeholder='自提门店' lay-search name="request_send_or_pick_up_store_guid"
                        lay-verify="required" lay-verType="tips"
                        class="fsSelect" dict="store" addNull="1">
                  <!--                                        <option value="">卡券名称</option>-->
                </select>
              </div>

              <div class="layui-inline" auth_admin="batch_update_express_route">
                <select placeholder='快递状态' name="express_no_route_status"
                        lay-verify="required" lay-verType="tips"
                        class="fsSelect" dict="express_status" addNull="1">
                  <!--                                        <option value="">卡券名称</option>-->
                </select>
              </div>

              <div class="layui-inline" auth_admin="batch_update_express_route">
                <select placeholder='快递2状态' name="express_no_2_route_status"
                        lay-verify="required" lay-verType="tips"
                        class="fsSelect" dict="express_status" addNull="1">
                  <!--                                        <option value="">卡券名称</option>-->
                </select>
              </div>

              <div class="layui-inline" auth_admin="show_request_send_or_pick_up_store">
                <select name="type" lay-verify="required" lay-search="">
                  <option value="">订单类型</option>
                  <option value="1">快递发货</option>
                  <option value="2">门店自提</option>
                </select>
              </div>
              <!--                            <div class="layui-input-inline">-->
              <!--                                <select name="key_name">-->
              <!--                                    <option value=""></option>-->
              <!--                                    <option value="extend_field_1">字段1</option>-->
              <!--                                    <option value="extend_field_2">字段2</option>-->
              <!--                                    <option value="extend_field_3">字段3</option>-->
              <!--                                </select>-->
              <!--                            </div>-->

              <!--                            <div class="layui-inline">-->
              <!--                                <select name="key" lay-verify="">-->
              <!--                                    <option value="bill_number">订单号</option>-->
              <!--                                    <option value="mobile">手机号</option>-->
              <!--                                    <option value="code">卡号</option>-->
              <!--                                    <option value="goods_info">商品名称</option>-->
              <!--                                    <option value="express_no">快递单号</option>-->
              <!--                                    <option value="true_name">姓名</option>-->
              <!--                                    <option value="address_info">地址</option>-->
              <!--                                    <option value="third_order_number">支付单号</option>-->
              <!--                                </select>-->
              <!--                            </div>-->
              <div class="layui-inline" auth_admin="extend_field/index">
                <input type="text" name="extend_field_1&extend_field_2&extend_field_3"
                       placeholder="扩展信息" autocomplete="off"
                       class="layui-input"/>
              </div>

              <div class="layui-inline layui-input-wrap" style="width: 345px">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                <input type="text" name="create_time" autocomplete="off"
                       class="layui-input fsDate" dateType="datetime" readonly dateRange="1"
                       placeholder="订单时间"/>
              </div>

              <div class="layui-inline layui-input-wrap">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                <input readonly type="text" name="request_send_or_pick_up_time" autocomplete="off"
                       class="layui-input fsDate" readonly dateRange="1" placeholder="预约时间"/>
              </div>

              <div class="layui-inline layui-input-wrap">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                <input readonly type="text" name="send_or_pick_up_time" autocomplete="off"
                       class="layui-input fsDate" readonly dateRange="1" placeholder="发货时间"/>
              </div>

              <div class="layui-inline layui-input-wrap" auth_admin="batch_update_express_route">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                <input readonly type="text" name="express_no_route_last_update_time" autocomplete="off"
                       class="layui-input fsDate" readonly dateRange="1" placeholder="快递更新时间"/>
              </div>

              <div class="layui-inline layui-input-wrap" auth_admin="batch_update_express_route">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-date"></i></div>
                <input readonly type="text" name="express_no_2_route_last_update_time"
                       autocomplete="off"
                       class="layui-input fsDate" readonly dateRange="1" placeholder="快递2更新时间"/>
              </div>
              <div class="layui-inline">
                <div class="layui-btn-container">
                  <button id="query" class="layui-btn" type="button" function="query"><i
                      class="layui-icon layui-icon-search"></i>查询
                  </button>
                  <button class="layui-btn layui-btn-normal" type="reset"><i
                      class="layui-icon layui-icon-delete"></i>重置
                  </button>

                  <button id="export" class="layui-btn" type="button"
                          onclick="export_note_url('/admin_api/v1/goods_order/export')"
                  ><i class="layui-icon">&#xe609;</i>导出
                  </button>
                  <button id="export_item" auth="goods_order/export_order_item" class="layui-btn"
                          type="button"
                          onclick="export_note_url('/admin_api/v1/goods_order/export_order_item')"
                  ><i class="layui-icon">&#xe609;</i>导出明细
                  </button>
                </div>

              </div>
            </div>
          </form>
          <div style="margin-bottom: 10px;margin-left: 10px;display: none">
            <button class="layui-btn layui-btn-primary layui-btn-sm" function="submit" url="#" isMutiDml="1"
                    isConfirm="1" confirmMsg="是否确定删除选中的数据？" inputs="id:" data-type="all"
                    data-events="del">
              删除
            </button>
            <button function="submit" url="/message/remark_is_read" isConfirm="1" confirmMsg="确认已读吗？"
                    inputs="id:"
                    class="layui-btn layui-btn-primary layui-btn-sm" data-type="all" data-events="ready">
              标记已读
            </button>
            <button function="submit" url="/message/all_is_read" isConfirm="1" confirmMsg="确认全部已读吗？"
                    class="layui-btn layui-btn-primary layui-btn-sm" data-type="all" data-events="readyAll">
              全部已读
            </button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" function="refresh"
                    data-type="all"><i class="layui-icon layui-icon-refresh"></i>刷新
            </button>
          </div>
          <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid" isLoad="1"
                 url="/goods_order/index"
                 isPage="1" defaultForm="query_form" height="auto" defaultToolbar="filter">
          </table>
          <div class="fsDatagridCols">
            <p checkbox="true" width="60" auth="goods_order/print_express_order"/>
            <!--<p type="numbers" title="#"/>-->
            <p field="way" templet="#wayTpl" title="来源" align="center" hide="true" width="90">
            <p field="bill_number" title="订单号" align="center" width="200">
            <p field="parent_bill_number" title="父订单号" align="center" hide="true" width="200"
               auth="cycle_delivery">
            <p field="cycle_times" title="期数" hide="true" align="center" width="80" auth="cycle_delivery">
            <p field="type" templet="#typeTpl" title="类型" align="center" hide="true" width="60">
            <p field="member_name" title="会员信息" align="center" hide="true" width="120"
               auth="member/index">
            <p field="share_member_name" title="分销者" align="center" hide="true" width="120"
               auth="member_distributor_apply_note/index">
            <p field="brokerage_money" title="佣金" align="center" hide="true" width="80"
               auth="member_distributor_apply_note/index">
            <p field="coupon_name" title="卡券名称" align="center" width="200" hide="true">
            <p field="goods_info_text" title="商品" align="center" width="250">
            <p field="code" title="卡号" align="center" width="120">
            <p field="user_id" title="卡券归属" dict="user" align="center" hide="true" width="140">
            <p field="code_send_remark" title="卡号备注" align="center" hide="true" width="140">
            <p field="owner_user_id" title="订单归属" dict="user" align="center" hide="true" width="140">
            <p field="request_send_or_pick_up_store_guid" title="自提门店" dict="store" align="center"
               hide="true" width="200" auth="store/index">
              <!--                        <p field="total_amount" title="总件数" align="center" hide="true" width="100" auth="mall">-->
            <p field="total_money" title="总价" align="center" hide="true" width="100" auth="mall">
            <p field="discount_preferential_money" title="优惠金额" align="center" hide="true" width="100"
               auth="discount">

            <p field="goods_money" title="商品金额" align="center" hide="true" width="100" auth="mall">
            <p field="paid_wechat" title="微信支付" align="center" hide="true" width="100" auth="mall">
            <p field="paid_money" title="余额支付" align="center" hide="true" width="100" auth="mall">
            <p field="paid_point" title="积分支付" align="center" hide="true" width="100" auth="mall">
            <p field="extra_charges_money" title="附加费" align="center" hide="true" width="100"
               auth="extra_charges_price">
            <p field="freight_money" title="运费" align="center" hide="true" width="100">
            <p field="true_name" title="姓名" align="center" width="100">
            <p field="mobile" title="手机号" align="center" width="120">
            <p field="id_card_number" title="身份证号" hide="true" align="center" width="120">
            <p field="address_info" title="地址" align="center" width="200">
            <p field="remark" title="备注" hide="true" align="center" width="120">
            <p field="status" templet="#statusTpl" title="状态" align="center" width="120">
            <p field="create_time" title="下单时间" align="center" width="180">
            <p field="request_send_or_pick_up_time" title="预约时间" align="center" width="120" sort="true">
            <p field="send_or_pick_up_time" align="center" title="发货时间" width="180" sort="true">
            <p field="print_times" align="center" title="打单状态" width="100"
               auth="goods_order/print_express_order" templet="#print_timesTpl">
            <p field="print_times" align="center" title="打单次数" width="100"
               auth="goods_order/print_express_order">
            <p field="send_or_pick_up_user_guid" title="发货人" dict="user_guid" width="180" hide="true">
            <p field="express_name" title="快递公司" width="180" align="center" hide="true">
            <p field="express_no" title="快递单号" width="180" align="center" hide="true">


            <p field="send_out_goods_remark" title="发货备注" width="180" align="center" hide="true">

            <p field="express_no_route_status" title="快递状态" dict="express_status"
               width="180" align="center"
               auth="batch_update_express_route" hide="true">

            <p field="express_no_route_last_update_time" title="快递更新时间" align="center" width="120"
               auth="batch_update_express_route" hide="true" sort="true">

            <p field="express_name_2" title="快递公司2" width="180" align="center"
               auth="multiple_express_send_out_goods" hide="true">
            <p field="express_no_2" title="快递单号2" width="180" align="center"
               auth="multiple_express_send_out_goods" hide="true">
            <p field="send_out_goods_remark_2" title="发货备注2" width="180" align="center"
               auth="multiple_express_send_out_goods" hide="true">

            <p field="express_no_2_route_status" title="快递2状态" dict="express_status"
               width="180" align="center" auth="batch_update_express_route" hide="true">

            <p field="express_no_2_route_last_update_time" title="快递2更新时间" align="center" width="120"
               auth="batch_update_express_route" hide="true" sort="true">


            <p field="extend_field_1" title="字段1" auth="extend_field/index" width="180" align="center"
               hide="true" auth="extend_field/index">
            <p field="extend_field_2" title="字段2" auth="extend_field/index" width="180" align="center"
               hide="true" auth="extend_field/index">
            <p field="extend_field_3" title="字段3" auth="extend_field/index" width="180"
               align="center"
               hide="true" auth="extend_field/index">
            <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="auto"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-inline">
    <!--        <button auth="goods_order/batch_send_out_goods" class="layui-btn layui-btn-sm" function="top"-->
    <!--                topUrl="/admin/goods_order/batch_send_out_goods" topMode="add"-->
    <!--                topWidth="800px"-->
    <!--                topHeight="600px" topTitle="批量发货">-->
    <!--            <i class="layui-icon">&#xe609;</i>批量发货-->
    <!--        </button>-->
    <button auth="goods_order/update_order_delete_time"
            class="layui-btn layui-btn-sm layui-btn-danger show_readonly"
            function="submit" url="/goods_order/update_order_delete_time" isMutiDml="1"
            isConfirm="1" confirmMsg="是否确定删除选中的订单？" inputs="guid:">
      <i class="layui-icon layui-icon-delete"></i>删除
    </button>

    <button auth="goods_order/re_notify" class="layui-btn layui-btn-sm layui-btn-normal"
            function="submit" url="/goods_order/re_notify" isMutiDml="1"
            isConfirm="1" confirmMsg="是否重发通知？" inputs="guid:">
      <i class="layui-icon layui-icon-speaker"></i>重发通知
    </button>

    <button auth="goods_order/batch_send_out_goods" class="layui-btn layui-btn-warm layui-btn-sm"
            function="batch_send_out_goods"
            isMutiDml="1" inputs="order_guid:$guid">
      <i class="layui-icon layui-icon-release"></i>
      批量发货
    </button>


    <button auth="goods_order/batch_edit" class="layui-btn layui-btn-normal  layui-btn-sm"
            function="batch_edit"
            isMutiDml="1" inputs="order_guid:$guid">
      <i class="layui-icon layui-icon-edit"></i>
      批量改地址
    </button>

    <button auth="goods_order/batch_del" class="layui-btn layui-btn-danger  layui-btn-sm"
            function="batch_del"
            isMutiDml="1" inputs="order_guid:$guid">
      <i class="layui-icon layui-icon-del"></i>
      批量回退
    </button>

    <button function="batch_print_express_order" auth="goods_order/print_express_order"
            class="layui-btn layui-btn-normal layui-btn-sm"
            url="/goods_order/print_express_order" isMutiDml="1">
      <i class="layui-icon layui-icon-file"></i>
      批量打单
    </button>

    <!--        <button class="layui-btn layui-btn-sm" function="refresh">-->
    <!--            <i class="layui-icon layui-icon-refresh"></i>刷新-->
    <!--        </button>-->
  </div>
</script>
<script type="text/html" id="barDemo">
  <div style="float: left">
    <button
        data-clipboard-text="{{ d.true_name }},{{ d.mobile }},{{ d.address_info }} {{ d.id_card_number ?  d.id_card_number : ''}}"
        class="btn layui-btn layui-btn-xs layui-btn-normal">复制
    </button>
    <a class="layui-btn layui-btn-xs" lay-event="top" topUrl="/admin/goods_order/detail"
       topWidth="800px" topHeight="90%" topTitle="详情" inputs="guid:">详情</a>

    {{# if(d.status == '1' && d.type == '1' && d.address_info && d.express_no_2 && !d.express_no){ }}
    <a auth="multiple_express_send_out_goods" class="layui-btn layui-btn-warm layui-btn-xs" lay-event="top"
       topUrl="/admin/goods_order/send_out_goods"
       topWidth="600px" topHeight="400px"
       topTitle="发包裹2【{{d.coupon_name}}】【{{ d.true_name }},{{ d.mobile }},{{ d.address_info }}】"
       topMode="add" inputs="guid:$guid,send_type:1,express_index:1">
      <i class="layui-icon layui-icon-release"></i>
      发货1</a>

    {{# } }}


    {{# if(d.status == '1' && d.type == '1' && d.address_info && d.express_no && !d.express_no_2){ }}
    <a auth="multiple_express_send_out_goods" class="layui-btn layui-btn-warm layui-btn-xs" lay-event="top"
       topUrl="/admin/goods_order/send_out_goods"
       topWidth="600px" topHeight="400px"
       topTitle="发包裹1【{{d.coupon_name}}】【{{ d.true_name }},{{ d.mobile }},{{ d.address_info }}】"
       topMode="add" inputs="guid:$guid,send_type:1,express_index:2">
      <i class="layui-icon layui-icon-release"></i>
      发货2</a>

    {{# } }}


    {{# if(d.status == '0'){ }}

    {{# if(d.type == '1' && d.address_info){ }}
    <a auth="goods_order/send_out_goods" class="layui-btn layui-btn-warm layui-btn-xs" lay-event="top"
       topUrl="/admin/goods_order/send_out_goods"
       topWidth="600px" topHeight="400px"
       topTitle="发货【{{d.coupon_name}}】【{{ d.true_name }},{{ d.mobile }},{{ d.address_info }}】"
       topMode="add" inputs="guid:$guid,send_type:1">
      <i class="layui-icon layui-icon-release"></i>
      发货</a>

    {{# } }}


    {{# if(d.type == '2' || !d.address_info){ }}
    <a auth="goods_order/send_out_goods" class="layui-btn layui-btn-normal layui-btn-xs"
       lay-event="send_out_goods">核销</a>

    {{# } }}


    {{# } }}


    {{# if(d.status == '1' && d.send_type==3){ }}


    <!--        <a auth="goods_order/print_express_order" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="top"-->
    <!--           topUrl="/admin/goods_order/batch_print_order"-->
    <!--           topWidth="600px" topHeight="400px"-->
    <!--           topTitle="打单【{{d.coupon_name}}】【{{ d.true_name }},{{ d.mobile }},{{ d.address_info }}】"-->
    <!--           topMode="add" inputs="guid:">-->
    <!--            <i class="layui-icon layui-icon-file"></i>-->
    <!--            打单</a>-->

    <a auth="goods_order/print_express_order" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="top"
       topUrl="/admin/goods_order/print_order"
       topWidth="500px" topHeight="800px"
       topTitle="打单【{{d.coupon_name}}】【{{ d.true_name }},{{ d.mobile }},{{ d.address_info }}】"
       topMode="add" inputs="guid:">
      <i class="layui-icon layui-icon-file"></i>
      打单</a>

    <a auth="goods_order/cancel_express_order" class="layui-btn layui-btn-danger layui-btn-xs"
       lay-event="cancel_express_order">撤单</a>
    {{# } }}


    {{# if( (d.way == 2 || (d.way==1 && d.paid_wechat>0)) && ( d.status == 0 ||d.status == 1)){ }}
    <a auth="goods_order/refund" class="layui-btn layui-btn-danger layui-btn-xs"
       lay-event="refund">退款</a>
    {{# } }}


    {{# if(d.status == '1' ){ }}
    <a auth="goods_order/confirm" class="layui-btn layui-btn-danger layui-btn-xs"
       lay-event="confirm">完成</a>
    {{# } }}


    {{# if(d.way == 1){ }}
    <a auth="goods_order/del" class="layui-btn layui-btn-danger layui-btn-xs"
       lay-event="del_order">回退</a>
    {{# } }}

  </div>
</script>

<script type="text/html" id="express_no_route_statusTplTpl">

  <!--    0  => '已揽件',-->
  <!--    1  => '暂无记录',-->
  <!--    2  => '在途中',-->
  <!--    3  => '派送中',-->
  <!--    4  => '已签收',-->
  <!--    5  => '用户拒签',-->
  <!--    6  => '疑难件',-->
  <!--    7  => '无效单',-->
  <!--    8  => '超时单',-->
  <!--    9  => '签收失败',-->
  <!--    10 => '退回',-->


  {{# if(d.express_no_route_status == '0'){ }}
  <button class="layui-btn layui-btn-warm layui-btn-xs">已揽件</button>
  {{# } else if(d.express_no_route_status == '1'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-orange">暂无记录</button>
  {{# } else if(d.express_no_route_status == '2'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-green">在途中</button>
  {{# } else if(d.express_no_route_status == '4'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-black">已签收</button>
  {{# } else if(d.express_no_route_status == '3'){ }}
  <button class="layui-btn layui-btn-disabled layui-btn-xs">派送中</button>
  {{# } else if(d.express_no_route_status == '10'){ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">退回</button>
  {{# } else{ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">{{d.express_no_route_status}}</button>
  {{#  } }}

</script>

<script type="text/html" id="express_no_2_route_statusTplTpl">
  {{d.express_no_2_route_status}}
</script>


<script type="text/html" id="statusTpl">
  {{# if(d.type == '1'){ }}
  {{# if(d.status == '-1'){ }}
  <button class="layui-btn layui-btn-warm layui-btn-xs">待支付</button>
  {{# } else if(d.status == '0'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-orange">待发货</button>
  {{# } else if(d.status == '1'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-green">已发货</button>
  {{# } else if(d.status == '2'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-black">已完成</button>
  {{# } else if(d.status == '-2'){ }}
  <button class="layui-btn layui-btn-disabled layui-btn-xs">已取消</button>
  {{# } else if(d.status == '-3'){ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">已退款</button>
  {{# } else{ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">未知状态</button>
  {{#  } }}
  {{#  } }}

  {{# if(d.type == '2'){ }}
  {{# if(d.status == '-1'){ }}
  <button class="layui-btn layui-btn-warm layui-btn-xs">待支付</button>
  {{# } else if(d.status == '0'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-orange">待提货</button>
  {{# } else if(d.status == '1'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-green">已提货</button>
  {{# } else if(d.status == '2'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-black">已完成</button>
  {{# } else if(d.status == '-2'){ }}
  <button class="layui-btn layui-btn-disabled layui-btn-xs">已取消</button>
  {{# } else{ }}
  <button class="layui-btn  layui-btn-danger layui-btn-xs">未知状态</button>
  {{#  } }}
  {{#  } }}


</script>
<script type="text/html" id="wayTpl">
  {{# if(d.way == '1'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-green">提货</button>
  {{# } else if(d.way == '2'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-orange">商城</button>
  {{# } else if(d.way == '3'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-blue">赠礼</button>
  {{# } else{ }}
  <span style="color: red">其他</span>

  {{#  } }}
</script>

<script type="text/html" id="print_timesTpl">
  {{# if(d.print_times == 0){ }}
  <button class="layui-btn layui-btn-disabled layui-btn-xs">未打单</button>
  {{# } else if(d.print_times > 0){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs layui-border-green">已打单</button>
  {{# } else{ }}
  <span style="color: red">其他</span>

  {{#  } }}


</script>

<script type="text/html" id="typeTpl">
  {{# if(d.type == '1'){ }}
  <span style="color: green">快递</span>
  {{# } else if(d.type == '2'){ }}
  <span style="color: blue">自提</span>
  {{# } else{ }}
  <span style="color: red">其他</span>

  {{#  } }}
</script>
<script>
  var clipboard = new ClipboardJS('.btn');
  clipboard.on('success', function (e) {
    //console.info('Action:', e.action);
    //console.info('Text:', e.text);
    //console.info('Trigger:', e.trigger);
    layer.msg("地址信息复制成功，你可用Ctrl+V 粘贴");
    e.clearSelection();
  });
  clipboard.on('error', function (e) {
    console.error('Action:', e.action);
    console.error('Trigger:', e.trigger);
  });
</script>


<script>
  layui.fsButton.batch_send_out_goods = function (elem, data, datagrid) {

    var guids = []
    let send_type = 2;
    for (var i = 0; i < data.length; i++) {
      console.log(data[i]['guid']);
      guids.push(data[i]['guid']);
    }
    if (!isEmpty(guids)) {
      // send_type = 3;
      // fsCommon.warnMsg("请选择需要批量发货的订单！");
      //return;
    }
    var ext = {
      'topUrl': '/admin/goods_order/send_out_goods?guid=' + guids.join(',') + '&send_type=' + send_type,
      // 'inputs': 'guid:',
      'topTitle': '批量发货',
      // 'isMaximize': 1,
      'topMode': 'add',
      'topHeight': '90%'
    };

    fsCommon.fsCommonOpen(ext, data, datagrid)

  };
  layui.fsButton.batch_edit = function (elem, data, datagrid) {
    var guids = []
    for (var i = 0; i < data.length; i++) {
      console.log(data[i]['guid']);
      guids.push(data[i]['guid']);
    }
    if (isEmpty(guids)) {
      fsCommon.warnMsg("请选择需要批量修改地址的订单！");
      return;
    }
    var ext = {
      'topUrl': '/admin/goods_order/batch_edit?guid=' + guids.join(','),
      // 'inputs': 'guid:',
      'topTitle': '批量改地址',
      // 'isMaximize': 1,
      'topMode': 'fsEdit',
      'topHeight': '80%'
    };

    fsCommon.fsCommonOpen(ext, data, datagrid)
  };
  layui.fsButton.batch_print_express_order = function (elem, data, datagrid) {
    var guids = []
    for (var i = 0; i < data.length; i++) {
      console.log(data[i]['guid']);
      guids.push(data[i]['guid']);
    }
    if (isEmpty(guids)) {
      fsCommon.warnMsg("请选择需要批量打单的订单！");
      return;
    }
    var ext = {
      'topUrl': '/admin/goods_order/batch_print_order?guid=' + guids.join(','),
      // 'inputs': 'guid:',
      'topTitle': '批量打单',
      // 'isMaximize': 1,
      'topMode': 'add',
      'topHeight': '80%'
    };

    fsCommon.fsCommonOpen(ext, data, datagrid)
  };
  layui.fsButton.batch_print_express_order_shunfeng = function (elem, data, datagrid) {
    var express_no = []
    for (var i = 0; i < data.length; i++) {
      console.log(data[i]['express_no']);
      if (data[i]['express_no']) {
        express_no.push(data[i]['express_no']);
      }
    }
    if (isEmpty(express_no)) {
      // fsCommon.warnMsg("请选择需要批量发货的订单！");
      //return;
    }

    var ext = {
      'topUrl': '/admin/goods_order/batch_print_order_shunfeng?express_no=' + express_no.join(','),
      // 'inputs': 'guid:',
      'topTitle': '批量打单',
      // 'isMaximize': 1,
      'topMode': 'add',
      'topHeight': '80%'
    };

    fsCommon.fsCommonOpen(ext, data, datagrid)
  };

  layui.fsButton.batch_del = function (elem, data, datagrid) {
    var guids = []
    for (var i = 0; i < data.length; i++) {
      console.log(data[i]['guid']);
      guids.push(data[i]['guid']);
    }
    if (isEmpty(guids)) {
      fsCommon.warnMsg("请选择需要批量回退的订单！");
      return;
    }

     //询问框
    layer.confirm('<b>确定要批量回退订单吗？(<span style="color: red"> 订单将彻底删除,卡券提货次数/金额将原路回退</span>)</b>', {
      btn: ['确定', '取消'],
      icon: 3,
    }, function () {
      //prompt层
      layer.prompt({ title: '请输入您的登陆密码，并确认', formType: 1 }, function (password, index) {
        layer.close(index);
        post_layui_admin_api_v1('/goods_order/batch_del', {
          order_guids: guids,
          password: password
        }, function (result) {
          layer.alert(result.msg, {
            'yes': function (index, layero) {
              datagrid.refresh();
              layer.close(index);
            }
          });
        });
      });
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };

  layui.fsButton.del_order = function (elem, data, datagrid) {
    let guid = data.guid;
    //询问框
    layer.confirm('<b>确定要回退订单吗？(<span style="color: red"> 订单将彻底删除,卡券提货次数/金额将原路回退</span>)</b>', {
      btn: ['确定', '取消'],
      icon: 3,
    }, function () {
      //prompt层
      layer.prompt({ title: '请输入您的登陆密码，并确认', formType: 1 }, function (password, index) {
        layer.close(index);
        post_layui_admin_api_v1('/goods_order/del', {
          order_guid: guid,
          password: password
        }, function (result) {
          layer.alert(result.msg, {
            'yes': function (index, layero) {
              datagrid.refresh();
              layer.close(index);
            }
          });
        });
      });
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.refund = function (elem, data, datagrid) {
    let guid = data.guid;
    //询问框
    layer.confirm('<b>确定要退单(<span style="color: red"> 资金将退给消费者</span>)吗？</b>', {
      btn: ['确定', '取消'],
      icon: 3,
    }, function () {
      //prompt层
      layer.prompt({ title: '请输入您的登陆密码，并确认', formType: 1 }, function (password, index) {
        layer.close(index);
        // layer.msg('code=' + code + '演示完毕！您的密码：' + password);
        post_layui_admin_api_v1('/goods_order/refund', {
          guid: guid,
          password: password
        }, function (result) {
          layer.alert(result.msg, {
            'yes': function (index, layero) {
              datagrid.refresh();
              layer.close(index);
            }
          });
        });
      });
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });


    // var guid = data.guid;
    // //询问框
    // layer.confirm('确定要退单吗？', {
    //     btn: ['确定', '取消'] //按钮
    // }, function () {
    //     post_layui_admin_api_v1('/goods_order/refund', {
    //         guid: guid,
    //     }, function (result) {
    //         layer.msg(result.msg, {
    //             icon: 1,
    //             time: 1000 //1秒关闭（如果不配置，默认是3秒）
    //         }, function () {
    //             //do something
    //             datagrid.refresh();
    //         });
    //     })
    // }, function () {
    //     //  layer.msg('您已经取消操作', {icon: 2});
    // });
  };
  layui.fsButton.send_out_goods = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定要核销该订单吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      post_layui_admin_api_v1('/goods_order/send_out_goods', {
        guid: guid,
        send_type: 4,
      }, function (result) {
        layer.msg(result.msg, {
          icon: 1,
          time: 1000 //1秒关闭（如果不配置，默认是3秒）
        }, function () {
          //do something
          datagrid.refresh();
        });
      })
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.confirm = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定要完成订单吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      post_layui_admin_api_v1('/goods_order/confirm', {
        order_guid: guid,
      }, function (result) {
        layer.msg(result.msg, {
          icon: 1,
          time: 1000 //1秒关闭（如果不配置，默认是3秒）
        }, function () {
          //do something
          datagrid.refresh();
        });
      })
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.print_express_order = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定要打印吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      post_layui_admin_api_v1('/goods_order/print_express_order', {
        order_guid: guid,
      }, function (result) {
        layer.open({
          type: 1,
          skin: 'layui-layer-rim', //加上边框
          area: ['100%', '90%'], //宽高
          content: '<div style="padding: 10px;">' + result.data.html + '</div>'
        });
      })
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.create_express_order = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定要下单吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      post_layui_admin_api_v1('/goods_order/create_express_order', {
        order_guid: guid,
      }, function (result) {
        layer.msg(result.msg, {
          icon: 1,
          time: 1000 //1秒关闭（如果不配置，默认是3秒）
        }, function () {
          //do something
          datagrid.refresh();
        });
        return;
      })
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  layui.fsButton.cancel_express_order = function (elem, data, datagrid) {
    var guid = data.guid;
    //询问框
    layer.confirm('确定要撤销快递单吗？', {
      btn: ['确定', '取消'] //按钮
    }, function () {
      post_layui_admin_api_v1('/goods_order/cancel_express_order', {
        order_guid: guid,
      }, function (result) {
        layer.msg(result.msg, {
          icon: 1,
          time: 1000 //1秒关闭（如果不配置，默认是3秒）
        }, function () {
          //do something
          datagrid.refresh();
        });
        return;
      })
    }, function () {
      //  layer.msg('您已经取消操作', {icon: 2});
    });
  };
  //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
  layui.use('element', function () {
    var element = layui.element;
    let request_send_or_pick_up_time = false;
    //今天待发
    element.on('tab(order_list)', function (data) {
      // console.log(this); //当前Tab标题所在的原始DOM元素
      // console.log(data.index); //得到当前Tab的所在下标
      // var status = '';
      let status = $(this).data('status');
      console.log(status); //得到当前Tab的所在下标
      if (status !== undefined) {
        request_send_or_pick_up_time = '';
      }
      let time_after_day = $(this).data('time_after_day');
      console.log(time_after_day); //得到当前Tab的所在下标
      if (time_after_day !== undefined) {
        var day1 = new Date();
        day1.setDate(day1.getDate() + time_after_day);
        status = 0;
        request_send_or_pick_up_time = day1.format("yyyy-MM-dd");
      }
      if (request_send_or_pick_up_time !== false) {
        request_send_or_pick_up_time = request_send_or_pick_up_time ? (request_send_or_pick_up_time + ' - ' + request_send_or_pick_up_time) : '';
        $("input[name='request_send_or_pick_up_time']").val(request_send_or_pick_up_time);
        request_send_or_pick_up_time = '';
      }
      $("input[name='status']").val(status);
      //按钮a点击完成后。b也执行点击事件。
      $('#query').trigger('click');
    });
    if (getQueryString('status') !== '') {
      element.tabChange('order_list', getQueryString('status'));
    }
  });

  function query() {
    $('#query').trigger('click');
    layer.msg('操作完成', {
      icon: 1,
      time: 3000
    }, function () {
      console.log('尝试关闭');
    });
  }

  function open_print_page(guid) {
    // let ext = {
    //     'topUrl': '/admin/goods_order/print_order',
    //     'topTitle': '打单',
    //     'inputs': 'guid:$guid',
    //     // 'isMaximize': 1,
    //     'topMode': 'add',
    //     'topHeight': '90%'
    // };
    // fsCommon.open('打单', '500', '90%', '/admin/goods_order/print_order?guid=' + guid, function () {
    // });
    // iframe 层
    layer.open({
      type: 2,
      title: '打单',
      shadeClose: true,
      shade: 0.8,
      area: ['380px', '80%'],
      content: '/admin/goods_order/print_order?guid=' + guid // iframe 的 url
    });
    // fsCommon.fsCommonOpen(ext, {guid: getQueryString('guid')});
  }
</script>